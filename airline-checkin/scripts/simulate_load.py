"""
Load simulation script to test concurrent seat selection.
"""
import asyncio
import aiohttp
import time
import random
from typing import List, Dict, Any
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import structlog

logger = structlog.get_logger(__name__)


class LoadSimulator:
    """Simulates concurrent load on the check-in system."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        self.results = []
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def simulate_concurrent_seat_selection(
        self,
        flight_id: str = "AI101",
        num_passengers: int = 50,
        target_seat: str = "12A"
    ):
        """
        Simulate multiple passengers trying to select the same seat.
        
        This test validates the concurrency control mechanism.
        """
        logger.info(
            "Starting concurrent seat selection simulation",
            flight_id=flight_id,
            num_passengers=num_passengers,
            target_seat=target_seat
        )
        
        # Generate passenger PNRs (using test passengers from seed data)
        passenger_pnrs = [f"TST{i:03d}" for i in range(1, num_passengers + 1)]
        
        # Create tasks for concurrent seat selection
        tasks = []
        for pnr in passenger_pnrs:
            task = asyncio.create_task(
                self._attempt_seat_selection(pnr, flight_id, target_seat)
            )
            tasks.append(task)
        
        # Execute all tasks concurrently
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Analyze results
        successful_selections = 0
        failed_selections = 0
        errors = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_selections += 1
                errors.append({
                    "pnr": passenger_pnrs[i],
                    "error": str(result),
                    "error_type": type(result).__name__
                })
            elif result.get("success"):
                successful_selections += 1
            else:
                failed_selections += 1
                errors.append({
                    "pnr": passenger_pnrs[i],
                    "error": result.get("error", "Unknown error"),
                    "status_code": result.get("status_code")
                })
        
        # Log results
        logger.info(
            "Concurrent seat selection simulation completed",
            total_attempts=num_passengers,
            successful_selections=successful_selections,
            failed_selections=failed_selections,
            duration_seconds=end_time - start_time,
            requests_per_second=num_passengers / (end_time - start_time)
        )
        
        # Verify that only one passenger got the seat
        if successful_selections == 1:
            logger.info("✅ Concurrency control working correctly - only one passenger got the seat")
        elif successful_selections == 0:
            logger.warning("⚠️ No passenger got the seat - possible system issue")
        else:
            logger.error(f"❌ Multiple passengers ({successful_selections}) got the same seat - concurrency control failed!")
        
        return {
            "total_attempts": num_passengers,
            "successful_selections": successful_selections,
            "failed_selections": failed_selections,
            "duration_seconds": end_time - start_time,
            "requests_per_second": num_passengers / (end_time - start_time),
            "errors": errors[:10],  # Show first 10 errors
        }
    
    async def _attempt_seat_selection(self, pnr: str, flight_id: str, seat_number: str) -> Dict[str, Any]:
        """Attempt to select a seat for a passenger."""
        try:
            # Add small random delay to simulate real-world timing variations
            await asyncio.sleep(random.uniform(0, 0.1))
            
            url = f"{self.base_url}/api/v1/checkin/select-seat"
            payload = {
                "pnr": pnr,
                "flight_id": flight_id,
                "seat_number": seat_number
            }
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "success": True,
                        "pnr": pnr,
                        "booking_id": data.get("booking_id"),
                        "status_code": response.status
                    }
                else:
                    error_data = await response.json()
                    return {
                        "success": False,
                        "pnr": pnr,
                        "error": error_data.get("error", {}).get("message", "Unknown error"),
                        "status_code": response.status
                    }
        
        except Exception as e:
            return {
                "success": False,
                "pnr": pnr,
                "error": str(e),
                "status_code": None
            }
    
    async def simulate_normal_load(
        self,
        flight_id: str = "AI101",
        num_passengers: int = 20,
        duration_seconds: int = 60
    ):
        """
        Simulate normal load with passengers selecting different seats.
        """
        logger.info(
            "Starting normal load simulation",
            flight_id=flight_id,
            num_passengers=num_passengers,
            duration_seconds=duration_seconds
        )
        
        # Get available seats
        seats = await self._get_available_seats(flight_id)
        if not seats:
            logger.error("No available seats found for load testing")
            return
        
        # Generate passenger PNRs
        passenger_pnrs = [f"TST{i:03d}" for i in range(1, num_passengers + 1)]
        
        start_time = time.time()
        successful_selections = 0
        failed_selections = 0
        
        # Simulate passengers selecting seats over time
        for i, pnr in enumerate(passenger_pnrs):
            if time.time() - start_time >= duration_seconds:
                break
            
            # Select a random available seat
            if seats:
                seat = random.choice(seats)
                result = await self._attempt_seat_selection(pnr, flight_id, seat["seat_number"])
                
                if result.get("success"):
                    successful_selections += 1
                    # Remove selected seat from available seats
                    seats = [s for s in seats if s["seat_number"] != seat["seat_number"]]
                else:
                    failed_selections += 1
            
            # Wait before next selection
            await asyncio.sleep(duration_seconds / num_passengers)
        
        end_time = time.time()
        
        logger.info(
            "Normal load simulation completed",
            successful_selections=successful_selections,
            failed_selections=failed_selections,
            duration_seconds=end_time - start_time
        )
        
        return {
            "successful_selections": successful_selections,
            "failed_selections": failed_selections,
            "duration_seconds": end_time - start_time,
        }
    
    async def _get_available_seats(self, flight_id: str) -> List[Dict[str, Any]]:
        """Get available seats for a flight."""
        try:
            url = f"{self.base_url}/api/v1/checkin/flights/{flight_id}/seats"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return [seat for seat in data["seats"] if seat["is_selectable"]]
                else:
                    logger.error(f"Failed to get seats: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Error getting seats: {e}")
            return []
    
    async def health_check(self) -> bool:
        """Check if the service is healthy."""
        try:
            url = f"{self.base_url}/health"
            async with self.session.get(url) as response:
                return response.status == 200
        except Exception:
            return False


async def main():
    """Main function to run load simulation."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Load simulation for airline check-in system")
    parser.add_argument("--url", default="http://localhost:8000", help="Base URL of the service")
    parser.add_argument("--test", choices=["concurrent", "normal", "both"], default="both", help="Type of test to run")
    parser.add_argument("--passengers", type=int, default=50, help="Number of passengers for concurrent test")
    parser.add_argument("--duration", type=int, default=60, help="Duration for normal load test")
    parser.add_argument("--flight", default="AI101", help="Flight ID to test")
    parser.add_argument("--seat", default="12A", help="Target seat for concurrent test")
    
    args = parser.parse_args()
    
    async with LoadSimulator(args.url) as simulator:
        # Health check
        if not await simulator.health_check():
            logger.error("Service health check failed. Make sure the service is running.")
            return
        
        logger.info("Service health check passed")
        
        if args.test in ["concurrent", "both"]:
            logger.info("Running concurrent seat selection test...")
            result = await simulator.simulate_concurrent_seat_selection(
                flight_id=args.flight,
                num_passengers=args.passengers,
                target_seat=args.seat
            )
            print("\n=== Concurrent Test Results ===")
            print(f"Total attempts: {result['total_attempts']}")
            print(f"Successful selections: {result['successful_selections']}")
            print(f"Failed selections: {result['failed_selections']}")
            print(f"Duration: {result['duration_seconds']:.2f} seconds")
            print(f"Requests per second: {result['requests_per_second']:.2f}")
        
        if args.test in ["normal", "both"]:
            logger.info("Running normal load test...")
            result = await simulator.simulate_normal_load(
                flight_id=args.flight,
                num_passengers=args.passengers,
                duration_seconds=args.duration
            )
            print("\n=== Normal Load Test Results ===")
            print(f"Successful selections: {result['successful_selections']}")
            print(f"Failed selections: {result['failed_selections']}")
            print(f"Duration: {result['duration_seconds']:.2f} seconds")


if __name__ == "__main__":
    asyncio.run(main())
