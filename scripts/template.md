Design something awesome
===

<!--ts-->
<!--te-->

# Problem Statement

Problem statement goes here.

Representative image goes here.

# Requirements

<!--rs-->
<!--re-->

## Core Requirements

 - core requirement 1
 - core requirement 2

##  High Level Requirements
<!--hs-->
<!--he-->

##  Micro Requirements
<!--ms-->
<!--me-->

# Output

## Design Document
<!--ds-->
<!--de-->

## Prototype

To understand the nuances and internals of this system, build a prototype that

- prototype requirement 1
- prototype requirement 2
- prototype requirement 3

###  Recommended Tech Stack

This is a recommended tech-stack for building this prototype

|Which|Options|
|-----|-----|
|Language|Golang, Java, C++|

###  Keep in mind

These are the common pitfalls that you should keep in mind while you are building this prototype

- pitfall-1
- pitfall-2
- pitfall-3

# Outcome

##  You'll learn

- learning 1
- learning 2
- learning 3

<!--fs-->
<!--fe-->
