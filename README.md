System Design Questions
===

The repository contains a set of problem statements around Software Architecture and System Design as conducted by [Arpit's System Design Masterclass](https://arpitbhayani.me/masterclass).


# Questions

 - [Design a Blogging Platform](blogging-platform.md)
 - [Design Online Offline Indicator](online-offline-indicator.md)
 - [Design Airline Check-in](airline-checkin.md)
 - [Design SQL backed KV Store](sql-kv.md)
 - Design Slack's Realtime Communication - NEW
 - [Design a Load Balancer](load-balancer.md)
 - [Design Synchronized Queue Consumers](queue-consumers.md)
 - [Design an Image Service](image-service.md)
 - [Design a HashTag Service](hashtag-service.md)
 - [Design OnePic](onepic.md)
 - [Design Photo Tagging](tagging-photos-with-people.md)
 - [Design User Affinity](user-affinity.md)
 - [Design Newly Unread Message Indicator](newly-unread-indicator.md)
 - [Design a Distributed Cache](distributed-cache.md)
 - [Design a Word Dictionary](word-dictionary.md)
 - [Design a Superfast KV Store](superfast-kv.md)
 - [Design S3](s3.md)
 - [Design a Faster Superfast KV Store](faster-superfast-kv.md)
 - [Design a Video Processing Pipeline for Steaming Service](video-pipeline.md)
 - [Design a Text-based Search Engine](text-search-engine.md)
 - [Design a service that serves Recent Searches for a user](recent-searches.md)
 - [Design a Text-based Cricket Commentary Service](live-commentary.md)
 - [Design a SQL backed Message Broker](sql-broker.md)
 - [Design a Distributed Task Scheduler](task-scheduler.md)
 - [Design Flash Sale](flash-sale.md)
 - [Design Counting Impressions at Scale](counting-impressions.md)
 - [Designing a Remote File Sync Service](file-sync.md)
 - [Designing a "who's near me" Service](near-me.md)

---

# Questions that I do not cover anymore

 - [Designing a Realtime DB](realtime-db.md)


# Arpit's System Design Masterclass

> A masterclass that helps you become great at designing _scalable_, _fault-tolerant_, and _highly available_ systems.

## The Program

This is a prime and intermediate-level cohort-based course aimed at providing an exclusive and crisp learning experience. The program will cover most of the topics under System Design and Software Architecture including but not limited to - _Architecting Social Networks_, _Building Storage Engines_ and, _Designing High Throughput Systems_.

The program will have a blend of _Live Classes happening on Weekends 4 to 6:30 pm IST_, _1:1 Mentorship sessions happening on weekdays_, and _assignments_. The program is designed to be intense and crisp to accelerate learning.


## Highlights

 - The course has been taken up by __200+__ people, spanning __7__ countries.
 - The NPS of the course is __89__.
 - People from companies like Tesla, Amazon, Microsoft, Google, Yelp, Github, Flipkart, Practo, Grab, PayPal, and many more, have taken up this course.


## Hi, I'm Arpit Bhayani 👋

<img width="256px" src="https://arpitbhayani.me/static/img/arpit.jpg" />

In my last **~9** years of experience, I have worked at **D. E. Shaw**, **Practo**, **Amazon**, and **Unacademy**; and have built systems, services, and platforms that scaled to billions.

Post my masters in CSE from **IIIT Hyderabad** I joined D. E. Shaw for a short stint of 2 months, before moving to Practo and working there as a **Platform Engineer**, building and owning close to 8 different microservices. Post Practo I worked at Amazon on their primary mission-critical E-Commerce Database and built **Data Pipelines** that cold tiered the stale data.

After quitting Amazon in 2018, I joined Unacademy as their first **Technical Architect** and there I designed, built, managed, and scaled services like _Search_, _Notification_, _Logging_, _Deployment Engine_, and many more. I have now transitioned into the role of a Sr. Engineering Manager, leading the Site Reliability vertical.

In January 2020, I started my [newsletter](https://arpitbhayani.me/newsletter) where I write and share an essay about programming languages internals, deep dives on some super-clever algorithms, and few tips on building scalable distributed systems. The newsletter currently has close to **2000+** subscribers.

Recently, I have started building [Revine](https://revine.arpitbhayani.me) - a programming langauge for kids helping them develop logic through **animations** and spark their creativity through **artwork**.

<center>
<a target="_blank" href="https://arpitbhayani.me/masterclass">
<img src="https://user-images.githubusercontent.com/4745789/137859181-d4499cf4-ce65-4466-8b88-a078ece0f081.PNG" width="300px" />
</a>
</center>
